# Mario AI 浏览器集成架构设计方案

## 📋 概述

本文档详细描述了将 MarioAI 功能集成到当前浏览器工程的完整架构设计，采用独立服务进程架构，支持 Node.js 和 Python 服务扩展。

## 🏗️ 整体架构设计

### 系统架构图

```
┌─────────────────┐    ┌─────────────────────┐    ┌─────────────────┐
│                 │    │   Electron App      │    │                 │
│   Browser UI    │    │                     │    │  AI Services    │
│                 │    │  ┌───────────────┐  │    │                 │
│ ┌─────────────┐ │    │  │ Main Process  │  │    │ ┌─────────────┐ │
│ │AI Toolbar   │ │    │  │ • Browser     │  │    │ │ Node.js     │ │
│ │• 📝 Notes   │ │◄──►│  │ • LibSQL      │  │◄──►│ │ AI Server   │ │
│ │• 🧠 Memory  │ │HTTP│  │ • Process Mgr │  │Proc│ │ • Chat      │ │
│ │• 📋 Clipboard│ │    │  └───────────────┘  │    │ │ • Notes     │ │
│ └─────────────┘ │    │                     │    │ │ • Memory    │ │
│                 │    │  ┌───────────────┐  │    │ │ • Clipboard │ │
│ ┌─────────────┐ │    │  │ Renderer      │  │    │ └─────────────┘ │
│ │AI Tabs      │ │    │  │ • Tab System  │  │    │                 │
│ │[💬Chat*]    │ │    │  │ • AI Modules  │  │    │ ┌─────────────┐ │
│ │[📝Notes]    │ │    │  └───────────────┘  │    │ │ Python      │ │
│ │[🧠Memory]   │ │    │                     │    │ │ Service     │ │
│ └─────────────┘ │    │                     │    │ │ • ML/AI     │ │
└─────────────────┘    └─────────────────────┘    │ │ • Analytics │ │
                                                  │ │ • Automation│ │
                                                  │ └─────────────┘ │
                                                  └─────────────────┘
```

### 详细通信架构图

```mermaid
graph TB
    subgraph "Electron Application"
        subgraph "Renderer Process"
            UI[Browser UI]
            AT[AI Toolbar]
            TABS[AI Tabs]
            CHAT[Chat Module]
            NOTES[Notes Module]
            MEMORY[Memory Module]
            CLIP[Clipboard Module]
        end

        subgraph "Main Process"
            MAIN[Main Process]
            PM[Process Manager]
            DB[(LibSQL Database)]
            IPC[IPC Bridge]
        end

        UI --> AT
        AT --> TABS
        TABS --> CHAT
        TABS --> NOTES
        TABS --> MEMORY
        TABS --> CLIP

        MAIN --> PM
        MAIN --> DB
        MAIN --> IPC
        IPC <--> UI
    end

    subgraph "AI Services"
        subgraph "Node.js AI Server :3001"
            EXPRESS[Express Server]
            CHAT_API[Chat API]
            NOTES_API[Notes API]
            MEMORY_API[Memory API]
            CLIP_API[Clipboard API]
            WS[WebSocket Server]
        end

        subgraph "Python Server :3002"
            FASTAPI[FastAPI Server]
            ML[ML Inference]
            ANALYSIS[Data Analysis]
            AUTO[Automation]
        end

        EXPRESS --> CHAT_API
        EXPRESS --> NOTES_API
        EXPRESS --> MEMORY_API
        EXPRESS --> CLIP_API
        EXPRESS --> WS

        FASTAPI --> ML
        FASTAPI --> ANALYSIS
        FASTAPI --> AUTO
    end

    PM -.->|spawn/manage| EXPRESS
    PM -.->|spawn/manage| FASTAPI

    CHAT -->|HTTP/WS| CHAT_API
    NOTES -->|HTTP| NOTES_API
    MEMORY -->|HTTP| MEMORY_API
    CLIP -->|HTTP| CLIP_API

    CHAT_API -->|HTTP| ML
    MEMORY_API -->|HTTP| ANALYSIS
    NOTES_API -->|HTTP| AUTO

    CHAT_API --> DB
    NOTES_API --> DB
    MEMORY_API --> DB
    CLIP_API --> DB
```

### 详细通信架构图

```mermaid
graph TB
    subgraph "Electron Application"
        subgraph "Renderer Process"
            UI[Browser UI]
            AT[AI Toolbar]
            TABS[AI Tabs]
            CHAT[Chat Module]
            NOTES[Notes Module]
            MEMORY[Memory Module]
            CLIP[Clipboard Module]
        end

        subgraph "Main Process"
            MAIN[Main Process]
            PM[Process Manager]
            DB[(LibSQL Database)]
            IPC[IPC Bridge]
        end

        UI --> AT
        AT --> TABS
        TABS --> CHAT
        TABS --> NOTES
        TABS --> MEMORY
        TABS --> CLIP

        MAIN --> PM
        MAIN --> DB
        MAIN --> IPC
        IPC <--> UI
    end

    subgraph "AI Services"
        subgraph "Node.js AI Server :3001"
            EXPRESS[Express Server]
            CHAT_API[Chat API]
            NOTES_API[Notes API]
            MEMORY_API[Memory API]
            CLIP_API[Clipboard API]
            WS[WebSocket Server]
        end

        subgraph "Python Server :3002"
            FASTAPI[FastAPI Server]
            ML[ML Inference]
            ANALYSIS[Data Analysis]
            AUTO[Automation]
        end

        EXPRESS --> CHAT_API
        EXPRESS --> NOTES_API
        EXPRESS --> MEMORY_API
        EXPRESS --> CLIP_API
        EXPRESS --> WS

        FASTAPI --> ML
        FASTAPI --> ANALYSIS
        FASTAPI --> AUTO
    end

    PM -.->|spawn/manage| EXPRESS
    PM -.->|spawn/manage| FASTAPI

    CHAT -->|HTTP/WS| CHAT_API
    NOTES -->|HTTP| NOTES_API
    MEMORY -->|HTTP| MEMORY_API
    CLIP -->|HTTP| CLIP_API

    CHAT_API -->|HTTP| ML
    MEMORY_API -->|HTTP| ANALYSIS
    NOTES_API -->|HTTP| AUTO

    CHAT_API --> DB
    NOTES_API --> DB
    MEMORY_API --> DB
    CLIP_API --> DB
```

### 详细通信架构图

#### 进程间通信详图
```
┌─────────────────────────────────────────────────────────────┐
│                    Electron Application                     │
│                                                             │
│  ┌─────────────────┐           ┌─────────────────────────┐  │
│  │ Renderer Process│           │    Main Process         │  │
│  │                 │    IPC    │                         │  │
│  │ • AI Toolbar    │◄─────────►│ • Process Manager       │  │
│  │ • AI Modules    │           │ • Service Health Check  │  │
│  │ • UI Components │           │ • Database Manager      │  │
│  └─────────────────┘           └─────────────────────────┘  │
│           │                                │                │
│           │ HTTP Requests                  │ Process Control│
│           ▼                                ▼                │
└─────────────────────────────────────────────────────────────┘
            │                                │
            │                                │
┌───────────▼────────────┐      ┌────────────▼────────────┐
│   Node.js AI Server    │      │    Python Server       │
│   Port: 3001           │◄────►│    Port: 3002          │
│                        │ HTTP │                        │
│ • Express Framework    │      │ • FastAPI Framework    │
│ • Chat API             │      │ • ML Inference         │
│ • Notes API            │      │ • Data Analysis        │
│ • Memory API           │      │ • Automation Scripts   │
│ • Clipboard API        │      │                        │
│ • WebSocket Server     │      │                        │
└────────────────────────┘      └─────────────────────────┘
            │
            │ Database Access
            ▼
┌─────────────────────────┐
│    LibSQL Database      │
│                         │
│ • AI Conversations      │
│ • Notes & Documents     │
│ • Memory & Knowledge    │
│ • Clipboard History     │
│ • Service Configuration │
└─────────────────────────┘
```

### 通信流程设计

#### 1. 用户交互流程
```
用户点击AI工具按钮 → 渲染进程创建AI标签页 → 加载AI模块组件 →
发起HTTP请求到AI服务 → 返回数据更新UI
```

#### 2. 服务启动流程
```
Electron主进程启动 → 进程管理器启动 →
启动Node.js AI服务 → 启动Python服务 →
健康检查确认服务可用 → 通知渲染进程服务就绪
```

#### 3. 数据流向
```
渲染进程 ←HTTP→ Node.js服务 ←HTTP→ Python服务
         ↓
    LibSQL数据库 ←→ Node.js服务
```

#### 4. 进程间通信详图
```
┌─────────────────────────────────────────────────────────────┐
│                    Electron Application                     │
│                                                             │
│  ┌─────────────────┐           ┌─────────────────────────┐  │
│  │ Renderer Process│           │    Main Process         │  │
│  │                 │    IPC    │                         │  │
│  │ • AI Toolbar    │◄─────────►│ • Process Manager       │  │
│  │ • AI Modules    │           │ • Service Health Check  │  │
│  │ • UI Components │           │ • Database Manager      │  │
│  └─────────────────┘           └─────────────────────────┘  │
│           │                                │                │
│           │ HTTP Requests                  │ Process Control│
│           ▼                                ▼                │
└─────────────────────────────────────────────────────────────┘
            │                                │
            │                                │
┌───────────▼────────────┐      ┌────────────▼────────────┐
│   Node.js AI Server    │      │    Python Server       │
│   Port: 3001           │◄────►│    Port: 3002          │
│                        │ HTTP │                        │
│ • Express Framework    │      │ • FastAPI Framework    │
│ • Chat API             │      │ • ML Inference         │
│ • Notes API            │      │ • Data Analysis        │
│ • Memory API           │      │ • Automation Scripts   │
│ • Clipboard API        │      │                        │
│ • WebSocket Server     │      │                        │
└────────────────────────┘      └─────────────────────────┘
            │
            │ Database Access
            ▼
┌─────────────────────────┐
│    LibSQL Database      │
│                         │
│ • AI Conversations      │
│ • Notes & Documents     │
│ • Memory & Knowledge    │
│ • Clipboard History     │
│ • Service Configuration │
└─────────────────────────┘
```

## 📁 详细目录结构

### 完整工程结构
```
mario-ai/
├── docs/
│   └── AI/
│       ├── mario-ai-integration-architecture.md    # 本文档
│       ├── api-specifications.md                   # API接口规范
│       └── development-guide.md                    # 开发指南
│
├── electron/                                       # Electron主应用
│   ├── src/
│   │   ├── main/
│   │   │   ├── services/
│   │   │   │   ├── ai-process-manager.ts          # 🆕 AI服务进程管理
│   │   │   │   ├── service-health-checker.ts      # 🆕 服务健康检查
│   │   │   │   └── database/                      # 现有数据库服务
│   │   │   ├── config/
│   │   │   │   └── ai-services.ts                 # 🆕 AI服务配置
│   │   │   └── main.ts                            # 修改：集成AI服务启动
│   │   ├── preload/
│   │   │   └── ai-bridge.ts                       # 🆕 AI服务通信桥接
│   │   └── resources/                             # 🆕 打包后的服务资源
│   │       ├── ai-server/                         # Node.js服务可执行文件
│   │       └── python-server/                     # Python服务可执行文件
│   └── package.json
│
├── web/browser/                                    # 浏览器前端
│   ├── src/
│   │   ├── views/app/
│   │   │   ├── components/
│   │   │   │   ├── AIToolbar/                     # 🆕 AI工具栏
│   │   │   │   │   ├── index.tsx                  # 主工具栏组件
│   │   │   │   │   ├── ToolButton.tsx             # 工具按钮组件
│   │   │   │   │   ├── ToolTooltip.tsx            # 工具提示组件
│   │   │   │   │   └── styles.css                 # 工具栏样式
│   │   │   │   ├── AITabs/                        # 🆕 AI标签页组件
│   │   │   │   │   ├── index.tsx                  # AI标签页容器
│   │   │   │   │   ├── ChatTab.tsx                # 对话标签页
│   │   │   │   │   ├── NotesTab.tsx               # 笔记标签页
│   │   │   │   │   ├── MemoryTab.tsx              # 记忆标签页
│   │   │   │   │   └── ClipboardTab.tsx           # 剪贴板标签页
│   │   │   │   └── App/
│   │   │   │       └── index.tsx                  # 修改：集成AI工具栏
│   │   │   ├── store/
│   │   │   │   ├── ai-tools.ts                    # 🆕 AI工具状态管理
│   │   │   │   └── tabs.ts                        # 修改：支持AI工具标签页
│   │   │   └── models/
│   │   │       └── tab.ts                         # 修改：扩展Tab模型
│   │   │
│   │   ├── ai-modules/                            # 🆕 AI功能模块
│   │   │   ├── chat/                              # 对话功能模块
│   │   │   │   ├── components/
│   │   │   │   │   ├── ChatInterface.tsx          # 从MarioAI迁移
│   │   │   │   │   ├── ChatInput.tsx              # 聊天输入组件
│   │   │   │   │   ├── MessageList.tsx            # 消息列表组件
│   │   │   │   │   ├── MessageItem.tsx            # 消息项组件
│   │   │   │   │   └── FileUpload.tsx             # 文件上传组件
│   │   │   │   ├── hooks/
│   │   │   │   │   ├── useChat.ts                 # 聊天逻辑Hook
│   │   │   │   │   ├── useChatHistory.ts          # 聊天历史Hook
│   │   │   │   │   └── useChatWebSocket.ts        # WebSocket连接Hook
│   │   │   │   ├── services/
│   │   │   │   │   └── chat-api.ts                # 聊天API服务
│   │   │   │   └── types/
│   │   │   │       └── chat.types.ts              # 聊天相关类型
│   │   │   │
│   │   │   ├── notes/                             # 笔记功能模块
│   │   │   │   ├── components/
│   │   │   │   │   ├── NotesManager.tsx           # 从MarioAI迁移
│   │   │   │   │   ├── BlockNoteEditor.tsx        # 富文本编辑器
│   │   │   │   │   ├── NotesList.tsx              # 笔记列表
│   │   │   │   │   ├── NotesSearch.tsx            # 笔记搜索
│   │   │   │   │   └── NotesToolbar.tsx           # 笔记工具栏
│   │   │   │   ├── hooks/
│   │   │   │   │   ├── useNotes.ts                # 笔记管理Hook
│   │   │   │   │   ├── useBlockNote.ts            # 编辑器Hook
│   │   │   │   │   └── useNotesSearch.ts          # 搜索Hook
│   │   │   │   ├── services/
│   │   │   │   │   └── notes-api.ts               # 笔记API服务
│   │   │   │   └── types/
│   │   │   │       └── notes.types.ts             # 笔记相关类型
│   │   │   │
│   │   │   ├── memory/                            # AI记忆管理模块
│   │   │   │   ├── components/
│   │   │   │   │   ├── MemoryManager.tsx          # 从MarioAI迁移
│   │   │   │   │   ├── MemoryList.tsx             # 记忆列表
│   │   │   │   │   ├── MemoryEditor.tsx           # 记忆编辑器
│   │   │   │   │   ├── MemorySearch.tsx           # 记忆搜索
│   │   │   │   │   └── MemoryVisualization.tsx    # 记忆可视化
│   │   │   │   ├── hooks/
│   │   │   │   │   ├── useMemory.ts               # 记忆管理Hook
│   │   │   │   │   ├── useMemorySearch.ts         # 记忆搜索Hook
│   │   │   │   │   └── useMemoryGraph.ts          # 记忆图谱Hook
│   │   │   │   ├── services/
│   │   │   │   │   └── memory-api.ts              # 记忆API服务
│   │   │   │   └── types/
│   │   │   │       └── memory.types.ts            # 记忆相关类型
│   │   │   │
│   │   │   ├── clipboard/                         # 剪贴板管理模块
│   │   │   │   ├── components/
│   │   │   │   │   ├── ClipboardManager.tsx       # 从MarioAI迁移
│   │   │   │   │   ├── ClipboardHistory.tsx       # 剪贴板历史
│   │   │   │   │   ├── ClipboardItem.tsx          # 剪贴板项
│   │   │   │   │   └── ClipboardSearch.tsx        # 剪贴板搜索
│   │   │   │   ├── hooks/
│   │   │   │   │   ├── useClipboard.ts            # 剪贴板Hook
│   │   │   │   │   └── useClipboardMonitor.ts     # 剪贴板监控Hook
│   │   │   │   ├── services/
│   │   │   │   │   └── clipboard-api.ts           # 剪贴板API服务
│   │   │   │   └── types/
│   │   │   │       └── clipboard.types.ts         # 剪贴板相关类型
│   │   │   │
│   │   │   └── shared/                            # AI模块共享资源
│   │   │       ├── components/
│   │   │       │   ├── AILoadingSpinner.tsx       # AI加载动画
│   │   │       │   ├── AIErrorBoundary.tsx        # AI错误边界
│   │   │       │   ├── AIModal.tsx                # AI模态框
│   │   │       │   └── AIStatusIndicator.tsx      # AI状态指示器
│   │   │       ├── hooks/
│   │   │       │   ├── useAIClient.ts             # 统一AI客户端Hook
│   │   │       │   ├── useAITheme.ts              # AI主题Hook
│   │   │       │   └── useAIServiceStatus.ts      # 服务状态Hook
│   │   │       ├── services/
│   │   │       │   ├── ai-client.ts               # 统一API客户端
│   │   │       │   └── websocket-client.ts        # WebSocket客户端
│   │   │       ├── types/
│   │   │       │   ├── common.types.ts            # 通用类型
│   │   │       │   └── api.types.ts               # API类型
│   │   │       └── utils/
│   │   │           ├── ai-helpers.ts              # AI辅助函数
│   │   │           ├── format-utils.ts            # 格式化工具
│   │   │           └── validation.ts              # 数据验证
│   │   │
│   │   └── services/
│   │       └── ai-service-client.ts               # 🆕 AI服务通信客户端
│   └── package.json
│
├── services/                                       # 🆕 独立服务目录
│   ├── ai-server/                                 # Node.js AI服务
│   │   ├── src/
│   │   │   ├── modules/
│   │   │   │   ├── chat/                          # 从MarioAI迁移
│   │   │   │   │   ├── routes.ts                  # 聊天路由
│   │   │   │   │   ├── controller.ts              # 聊天控制器
│   │   │   │   │   ├── service.ts                 # 聊天服务
│   │   │   │   │   └── websocket.ts               # WebSocket处理
│   │   │   │   ├── notes/                         # 笔记服务
│   │   │   │   │   ├── routes.ts                  # 笔记路由
│   │   │   │   │   ├── controller.ts              # 笔记控制器
│   │   │   │   │   └── service.ts                 # 笔记服务
│   │   │   │   ├── memory/                        # 记忆服务
│   │   │   │   │   ├── routes.ts                  # 记忆路由
│   │   │   │   │   ├── controller.ts              # 记忆控制器
│   │   │   │   │   ├── service.ts                 # 记忆服务
│   │   │   │   │   └── vector-store.ts            # 向量存储
│   │   │   │   └── clipboard/                     # 剪贴板服务
│   │   │   │       ├── routes.ts                  # 剪贴板路由
│   │   │   │       ├── controller.ts              # 剪贴板控制器
│   │   │   │       └── service.ts                 # 剪贴板服务
│   │   │   ├── middleware/
│   │   │   │   ├── auth.ts                        # 认证中间件
│   │   │   │   ├── cors.ts                        # CORS中间件
│   │   │   │   └── error-handler.ts               # 错误处理
│   │   │   ├── database/
│   │   │   │   ├── connection.ts                  # 数据库连接
│   │   │   │   └── migrations/                    # 数据库迁移
│   │   │   ├── config/
│   │   │   │   ├── database.ts                    # 数据库配置
│   │   │   │   └── services.ts                    # 服务配置
│   │   │   └── index.ts                           # 服务入口
│   │   ├── package.json
│   │   └── tsconfig.json
│   │
│   └── python-server/                             # Python服务
│       ├── src/
│       │   ├── main.py                            # 服务入口
│       │   ├── modules/
│       │   │   ├── ml_inference/                  # 机器学习推理
│       │   │   │   ├── __init__.py
│       │   │   │   ├── routes.py                  # ML路由
│       │   │   │   ├── models.py                  # ML模型
│       │   │   │   └── service.py                 # ML服务
│       │   │   ├── data_analysis/                 # 数据分析
│       │   │   │   ├── __init__.py
│       │   │   │   ├── routes.py                  # 分析路由
│       │   │   │   └── analyzer.py                # 分析器
│       │   │   └── automation/                    # 自动化脚本
│       │   │       ├── __init__.py
│       │   │       ├── routes.py                  # 自动化路由
│       │   │       └── scripts.py                 # 脚本执行器
│       │   ├── config/
│       │   │   └── settings.py                    # Python服务配置
│       │   └── utils/
│       │       ├── helpers.py                     # 辅助函数
│       │       └── validators.py                  # 数据验证
│       ├── requirements.txt                       # Python依赖
│       └── pyproject.toml                         # Python项目配置
│
├── shared/src/                                     # 共享类型和常量
│   ├── types/
│   │   ├── ai-tools.ts                            # 🆕 AI工具类型
│   │   ├── tab.ts                                 # 修改：扩展标签页类型
│   │   └── services.ts                            # 🆕 服务通信类型
│   └── constants/
│       ├── ai-constants.ts                        # 🆕 AI相关常量
│       └── service-ports.ts                       # 🆕 服务端口配置
│
├── scripts/                                        # 构建和开发脚本
│   ├── build-services.js                          # 🆕 服务构建脚本
│   ├── dev-services.js                            # 🆕 服务开发脚本
│   ├── package-services.js                        # 🆕 服务打包脚本
│   └── health-check.js                            # 🆕 服务健康检查脚本
│
└── package.json                                    # 根package.json
```

## 🔧 核心组件设计

### 1. AI工具栏 (AIToolbar)
- **位置**: 浏览器界面左侧通栏
- **功能**: 提供AI功能入口按钮
- **交互**: 点击按钮创建对应的AI工具标签页

### 2. AI标签页系统 (AITabs)
- **集成**: 扩展现有标签页系统
- **类型**: 支持 'web' 和 'ai-tool' 两种类型
- **固定标签**: 对话功能作为固定标签页，不可删除移动

### 3. 进程管理器 (AIProcessManager)
- **职责**: 管理AI服务进程的生命周期
- **功能**: 启动、停止、重启、健康检查
- **监控**: 服务状态监控和自动恢复

### 4. 服务通信客户端 (AIClient)
- **统一接口**: 为所有AI功能提供统一的API调用接口
- **错误处理**: 统一的错误处理和重试机制
- **类型安全**: 完整的TypeScript类型支持

## 📊 数据库设计扩展

### LibSQL Schema 扩展
```sql
-- AI对话相关表
CREATE TABLE ai_conversations (
  id TEXT PRIMARY KEY,
  title TEXT,
  user_id TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE ai_messages (
  id TEXT PRIMARY KEY,
  conversation_id TEXT REFERENCES ai_conversations(id),
  role TEXT NOT NULL, -- 'user', 'assistant', 'system'
  content TEXT NOT NULL,
  metadata TEXT, -- JSON格式的元数据
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- AI记忆相关表
CREATE TABLE ai_memories (
  id TEXT PRIMARY KEY,
  title TEXT,
  content TEXT NOT NULL,
  context TEXT,
  embedding TEXT, -- 向量存储
  tags TEXT, -- JSON数组
  importance_score REAL DEFAULT 0.5,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 笔记相关表
CREATE TABLE ai_notes (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL, -- BlockNote JSON格式
  tags TEXT, -- JSON数组
  folder_id TEXT,
  is_favorite BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 剪贴板历史表
CREATE TABLE ai_clipboard_history (
  id TEXT PRIMARY KEY,
  content TEXT NOT NULL,
  content_type TEXT DEFAULT 'text', -- 'text', 'image', 'file'
  source_app TEXT,
  file_path TEXT, -- 文件类型时的路径
  thumbnail TEXT, -- 图片缩略图
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- AI服务配置表
CREATE TABLE ai_service_config (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  service_name TEXT NOT NULL, -- 'ai-server', 'python-server'
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🚀 部署和构建策略

### 开发模式
```bash
# 启动所有服务进行开发
pnpm dev

# 单独启动服务
pnpm dev:ai-server
pnpm dev:python-server
pnpm dev:electron
pnpm dev:web
```

### 生产构建
```bash
# 构建所有服务
pnpm build:services

# 打包AI服务到resources目录
pnpm package:services

# 构建完整应用
pnpm build

# 生成安装包
pnpm dist
```

### 服务打包策略
1. **Node.js服务**: 使用esbuild打包成单文件
2. **Python服务**: 使用PyInstaller打包成可执行文件
3. **资源嵌入**: 将服务文件嵌入到Electron应用resources目录

## 📈 性能和扩展性考虑

### 性能优化
1. **进程隔离**: AI服务不影响浏览器性能
2. **懒加载**: AI模块按需加载
3. **缓存策略**: 合理的数据缓存机制
4. **资源管理**: 监控和限制服务资源使用

### 扩展性设计
1. **模块化**: 每个AI功能独立模块
2. **插件化**: 支持第三方AI功能插件
3. **微服务**: 支持服务的独立部署和扩展
4. **云端集成**: 预留云端服务集成接口

## 🔒 安全性考虑

### 通信安全
1. **本地通信**: 服务仅监听localhost
2. **端口管理**: 动态端口分配避免冲突
3. **数据加密**: 敏感数据加密存储

### 权限控制
1. **进程隔离**: 服务进程权限最小化
2. **文件访问**: 限制服务的文件系统访问
3. **网络访问**: 控制服务的网络访问权限

## 📝 开发指南

### 添加新AI功能的步骤
1. 在 `ai-modules/` 下创建新功能模块
2. 在 `services/ai-server/` 下添加对应的API服务
3. 在 `AIToolbar` 中添加功能按钮
4. 在 `AITabs` 中添加标签页组件
5. 更新类型定义和数据库schema

### 代码规范
1. **TypeScript**: 严格的类型检查
2. **ESLint**: 统一的代码风格
3. **组件规范**: 统一的组件结构和命名
4. **API规范**: RESTful API设计原则

## 🎯 实施计划

### 第一阶段 (2-3周): 基础架构
- [ ] 创建服务目录结构
- [ ] 实现进程管理器
- [ ] 搭建AI工具栏和标签页系统
- [ ] 建立服务通信机制

### 第二阶段 (3-4周): 核心功能迁移
- [ ] 迁移对话功能 (固定标签页)
- [ ] 迁移笔记功能
- [ ] 迁移记忆管理功能
- [ ] 迁移剪贴板功能

### 第三阶段 (2-3周): Python服务集成
- [ ] 搭建Python服务框架
- [ ] 实现机器学习推理服务
- [ ] 实现数据分析服务
- [ ] 实现自动化脚本服务

### 第四阶段 (1-2周): 优化和完善
- [ ] 性能优化和测试
- [ ] 用户体验改进
- [ ] 文档完善
- [ ] 部署和发布准备

---

**文档版本**: v1.0  
**创建日期**: 2024-01-31  
**最后更新**: 2024-01-31  
**维护者**: Mario AI Team
