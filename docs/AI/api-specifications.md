# Mario AI 服务 API 规范

## 📋 概述

本文档定义了 Mario AI 浏览器集成项目中各个服务的 API 接口规范，包括 Node.js AI 服务和 Python 服务的详细接口定义。

## 🌐 服务端点

### Node.js AI 服务
- **基础URL**: `http://localhost:3001`
- **健康检查**: `GET /health`

### Python 服务
- **基础URL**: `http://localhost:3002`
- **健康检查**: `GET /health`

## 🔧 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {}, // 具体数据
  "message": "操作成功",
  "timestamp": "2024-01-31T10:00:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {} // 详细错误信息
  },
  "timestamp": "2024-01-31T10:00:00Z"
}
```

## 💬 聊天服务 API

### 发送消息
```http
POST /api/chat/message
Content-Type: application/json

{
  "message": "用户消息内容",
  "sessionId": "会话ID（可选）",
  "model": "AI模型名称（可选）",
  "context": {} // 上下文信息（可选）
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "消息ID",
    "message": "AI回复内容",
    "sessionId": "会话ID",
    "model": "使用的模型",
    "usage": {
      "promptTokens": 100,
      "completionTokens": 150,
      "totalTokens": 250
    }
  }
}
```

### 获取会话历史
```http
GET /api/chat/history/{sessionId}?limit=50&offset=0
```

### 创建新会话
```http
POST /api/chat/session
Content-Type: application/json

{
  "title": "会话标题（可选）"
}
```

### WebSocket 连接
```
ws://localhost:3001/api/chat/ws?sessionId={sessionId}
```

## 📝 笔记服务 API

### 创建笔记
```http
POST /api/notes
Content-Type: application/json

{
  "title": "笔记标题",
  "content": {}, // BlockNote JSON格式
  "tags": ["标签1", "标签2"],
  "folderId": "文件夹ID（可选）"
}
```

### 获取笔记列表
```http
GET /api/notes?page=1&limit=20&search=关键词&tags=tag1,tag2&folderId=folder123
```

### 获取单个笔记
```http
GET /api/notes/{noteId}
```

### 更新笔记
```http
PUT /api/notes/{noteId}
Content-Type: application/json

{
  "title": "更新的标题",
  "content": {}, // BlockNote JSON格式
  "tags": ["新标签"]
}
```

### 删除笔记
```http
DELETE /api/notes/{noteId}
```

### 搜索笔记
```http
GET /api/notes/search?q=搜索关键词&type=title|content|all
```

## 🧠 记忆管理 API

### 创建记忆
```http
POST /api/memory
Content-Type: application/json

{
  "title": "记忆标题",
  "content": "记忆内容",
  "context": "上下文信息",
  "tags": ["标签1", "标签2"],
  "importance": 0.8 // 重要性评分 0-1
}
```

### 搜索记忆
```http
POST /api/memory/search
Content-Type: application/json

{
  "query": "搜索查询",
  "type": "semantic|keyword|hybrid", // 搜索类型
  "limit": 10,
  "threshold": 0.7 // 相似度阈值
}
```

### 获取记忆列表
```http
GET /api/memory?page=1&limit=20&sortBy=importance|created_at&order=desc
```

### 更新记忆
```http
PUT /api/memory/{memoryId}
Content-Type: application/json

{
  "title": "更新的标题",
  "content": "更新的内容",
  "importance": 0.9
}
```

### 删除记忆
```http
DELETE /api/memory/{memoryId}
```

### 获取记忆关联
```http
GET /api/memory/{memoryId}/related?limit=5
```

## 📋 剪贴板服务 API

### 获取剪贴板历史
```http
GET /api/clipboard/history?page=1&limit=50&type=text|image|file
```

### 添加剪贴板项
```http
POST /api/clipboard
Content-Type: application/json

{
  "content": "剪贴板内容",
  "type": "text|image|file",
  "sourceApp": "来源应用",
  "metadata": {} // 额外元数据
}
```

### 获取剪贴板项
```http
GET /api/clipboard/{itemId}
```

### 删除剪贴板项
```http
DELETE /api/clipboard/{itemId}
```

### 清空剪贴板历史
```http
DELETE /api/clipboard/history?olderThan=7d
```

### 搜索剪贴板
```http
GET /api/clipboard/search?q=搜索关键词&type=text|image|file
```

## 🐍 Python 服务 API

### 机器学习推理

#### 文本分类
```http
POST /api/ml/classify
Content-Type: application/json

{
  "text": "要分类的文本",
  "model": "分类模型名称",
  "options": {} // 模型选项
}
```

#### 情感分析
```http
POST /api/ml/sentiment
Content-Type: application/json

{
  "text": "要分析的文本",
  "language": "zh|en" // 语言
}
```

#### 文本摘要
```http
POST /api/ml/summarize
Content-Type: application/json

{
  "text": "要摘要的文本",
  "maxLength": 200,
  "language": "zh|en"
}
```

### 数据分析

#### 数据统计分析
```http
POST /api/analysis/stats
Content-Type: application/json

{
  "data": [], // 数据数组
  "columns": ["col1", "col2"], // 要分析的列
  "analysisType": "descriptive|correlation|regression"
}
```

#### 数据可视化
```http
POST /api/analysis/visualize
Content-Type: application/json

{
  "data": [], // 数据数组
  "chartType": "line|bar|scatter|histogram",
  "xColumn": "x轴列名",
  "yColumn": "y轴列名",
  "options": {} // 图表选项
}
```

### 自动化脚本

#### 执行Python脚本
```http
POST /api/automation/execute
Content-Type: application/json

{
  "script": "Python脚本代码",
  "timeout": 30, // 超时时间（秒）
  "environment": {} // 环境变量
}
```

#### 获取脚本模板
```http
GET /api/automation/templates?category=web|data|system
```

#### 保存脚本
```http
POST /api/automation/scripts
Content-Type: application/json

{
  "name": "脚本名称",
  "description": "脚本描述",
  "code": "Python代码",
  "category": "分类",
  "tags": ["标签"]
}
```

## 🔐 认证和授权

### API密钥认证
```http
Authorization: Bearer {api_key}
```

### 会话认证
```http
Cookie: session_id={session_id}
```

## 📊 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

## 🚀 WebSocket 事件

### 聊天事件
```javascript
// 连接
ws.send(JSON.stringify({
  type: 'join',
  sessionId: 'session123'
}));

// 发送消息
ws.send(JSON.stringify({
  type: 'message',
  content: '用户消息'
}));

// 接收消息
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  // data.type: 'message' | 'typing' | 'error'
};
```

### 系统事件
```javascript
// 服务状态变化
{
  type: 'service_status',
  service: 'ai-server|python-server',
  status: 'online|offline|error'
}

// 进度更新
{
  type: 'progress',
  taskId: 'task123',
  progress: 0.5, // 0-1
  message: '处理中...'
}
```

## 🔧 错误代码

### 通用错误
- `INVALID_REQUEST`: 请求格式错误
- `MISSING_PARAMETER`: 缺少必需参数
- `INVALID_PARAMETER`: 参数值无效
- `UNAUTHORIZED`: 未授权访问
- `FORBIDDEN`: 禁止访问
- `NOT_FOUND`: 资源不存在
- `RATE_LIMITED`: 请求频率限制
- `INTERNAL_ERROR`: 服务器内部错误

### 服务特定错误
- `CHAT_SESSION_NOT_FOUND`: 聊天会话不存在
- `NOTE_NOT_FOUND`: 笔记不存在
- `MEMORY_SEARCH_FAILED`: 记忆搜索失败
- `CLIPBOARD_EMPTY`: 剪贴板为空
- `ML_MODEL_NOT_LOADED`: 机器学习模型未加载
- `SCRIPT_EXECUTION_FAILED`: 脚本执行失败

## 📝 使用示例

### JavaScript 客户端示例
```javascript
class AIClient {
  constructor(baseURL = 'http://localhost:3001') {
    this.baseURL = baseURL;
  }
  
  async chat(message, sessionId) {
    const response = await fetch(`${this.baseURL}/api/chat/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ message, sessionId })
    });
    
    return response.json();
  }
  
  async createNote(title, content, tags = []) {
    const response = await fetch(`${this.baseURL}/api/notes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ title, content, tags })
    });
    
    return response.json();
  }
}
```

### Python 客户端示例
```python
import requests
import json

class AIClient:
    def __init__(self, base_url="http://localhost:3001"):
        self.base_url = base_url
    
    def chat(self, message, session_id=None):
        url = f"{self.base_url}/api/chat/message"
        data = {"message": message}
        if session_id:
            data["sessionId"] = session_id
        
        response = requests.post(url, json=data)
        return response.json()
    
    def search_memory(self, query, search_type="semantic", limit=10):
        url = f"{self.base_url}/api/memory/search"
        data = {
            "query": query,
            "type": search_type,
            "limit": limit
        }
        
        response = requests.post(url, json=data)
        return response.json()
```

---

**文档版本**: v1.0  
**创建日期**: 2024-01-31  
**最后更新**: 2024-01-31  
**维护者**: Mario AI Team
